# Database_Maria 필수 규칙 및 사용법

## 🔴 핵심 원칙

### 1. CID별 순서 보장 (가장 중요)
- **같은 CID의 모든 DB 작업은 순차적으로 처리됨**
- **CID별 시퀀스(SEQ) 자동 증가로 순서 보장**

### 2. 비동기 처리 및 게임 스레드 복귀
- **게임 로직: 싱글스레드**
- **DB 워커: 멀티스레드 (최대 32개)**
- 게임 스레드에서 DB 요청 → DB 워커 스레드에서 처리 → **반드시 싱글 게임 스레드로 결과 반환**
- 모든 게임 상태 변경은 싱글 게임 스레드에서만 발생 (thread-safe)

### 3. 고효율 논블로킹 설계 목표
- **커넥션 풀: 32개, 워커 스레드: 16개**
- **각 워커는 여러 CID를 논블로킹으로 처리**
- **워커는 쿼리 전송 후 즉시 다른 CID 처리 가능**
- **어떤 워커든 어떤 CID든 처리 가능 (CID-스레드 고정 매핑 없음)**
- **콜백 처리도 어떤 워커든 가능하나, 결과는 반드시 게임 스레드로**

### 4. 커넥션 대기 큐 시스템 (2024.12 업데이트)
- **모든 쿼리 실행은 커넥션 대기 큐를 통해 처리**
- **커넥션 부족 시 대기 큐에 등록, 스레드 블로킹 없음**
- **커넥션 반환 시 대기 작업 자동 실행**
- **CID별 순서 보장과 공정한 커넥션 분배 동시 달성**

### 5. CID 작업 큐 동작 방식
- 각 CID마다 독립적인 작업 큐 존재 (CID 작업 큐)
- 전체 시스템에 하나의 커넥션 대기 큐 존재

**새 작업 도착 시:**
- CID 작업 큐가 비어있으면: 커넥션 있으면 즉시 실행, 없으면 커넥션 대기 큐에 등록
- CID 작업 큐에 이미 작업이 있으면: CID 작업 큐에 추가

**작업 완료 시:**
- 해당 CID 작업 큐에서 다음 작업 확인
- 다음 작업이 있으면: 무조건 커넥션 대기 큐에 등록 (공정성 보장)
- 커넥션 대기 큐가 모든 CID의 작업을 순서대로 처리

### 6. MariaDB 클라이언트 DLL 전용
- **무조건 MariaDB Connector/C 사용**
- **MySQL 호환성 고려 불필요 (조건부 컴파일 금지)**
- **MariaDB 비동기 API 적극 활용**

## 📋 RecordSet 사용 패턴

### 1️⃣ 단일 행 결과 (레코드가 하나)
```cpp
auto recordSet = queryData->GetRecordSet();
if (recordSet == nullptr)
    return false;

// IsEOF() 호출 없이 바로 데이터 읽기
uint64_t deleteStamptime = 0;
recordSet->GetItem("DeleteReqAt", deleteStamptime);
```

### 2️⃣ 다중 행 결과 (레코드가 여러 개)
```cpp
auto recordSet = queryData->GetRecordSet();
while (!recordSet->IsEOF())  // IsEOF()가 자동으로 다음 행으로 이동
{
    int64_t cid;
    std::string name;
    int32_t level;
    
    recordSet->GetItem("CID", cid);
    recordSet->GetItem("Name", name);
    recordSet->GetItem("Level", level);
    
    // 필요시 continue로 특정 행 스킵
    if (level < 10)
        continue;
        
    // 데이터 처리...
}
```

### 3️⃣ 배치 프로시저 결과 (여러 프로시저, 여러 RecordSet)
```cpp
// NSStoredProcedureBatch 사용 시 각 프로시저별 RecordSet 획득
auto characterRecordSet = queryData->GetRecordSet(SpGetCharacterList::GetName());
auto equipRecordSet = queryData->GetRecordSet(SpGetEquipmentListForLobby::GetName());

if (characterRecordSet == nullptr || equipRecordSet == nullptr)
{
    LOGD << "Failed to get recordSet";
    return;
}

// 각 RecordSet 독립적으로 처리
while (!characterRecordSet->IsEOF())
{
    NPCharacterListInfo info;
    characterRecordSet->GetItem("CID", info.CID);
    characterRecordSet->GetItem("Name", info.Name);
    // ...
}

while (!equipRecordSet->IsEOF())
{
    // 장비 정보 처리...
}
```

## 🎯 호출 방법

### 1. 일반 쿼리/프로시저 호출
```cpp
// 비동기 호출 - Promise 패턴
auto promise = NSDataBaseManager::GetInstance()->StartQuery<SpGetUserInfo>(
    connection,
    serializer,
    transactionId
);

// 결과 처리
promise.Then([](auto queryData) {
    auto recordSet = queryData->GetRecordSet();
    // 결과 처리...
});
```

### 2. Storage 컴포넌트 업데이트
```cpp
// NSStorageUpdater를 통한 컴포넌트 업데이트
void NSStorageUpdater::Commit(
    const TYPE_RESULT_MODEL_FUNC& resultFunc, 
    std::source_location location)
{
    QueryFunc(EStorageUpdateMethod::Normal, resultFunc, location, false, 0);
}
```

## ⚠️ 주의사항

### 1. RecordSet 사용 규칙
- **단일 행**: IsEOF() 호출 없이 바로 GetItem() 사용
- **다중 행**: while (!recordSet->IsEOF()) 패턴 필수
- **IsEOF()는 자동으로 다음 행으로 이동** (별도 MoveNext() 불필요)

### 2. 스레드 라우팅 규칙
- **CID는 특정 워커 스레드에 고정되지 않음**
- **어떤 워커든 어떤 CID든 처리 가능**
- **CID별 작업 큐로 순서 보장**
- **결과는 반드시 싱글 게임 스레드로 반환**

### 3. API 호환성
- 기존 게임서버 코드 수정 불필요
- `GetAdoRecordSet()` → `GetRecordSet()` 이름만 변경
- 나머지 인터페이스는 100% 호환

### 4. 빌드정합성 , 빌드가능 예외처리
- 라이브러리에 정의되지 않았는데 사용중인 ENUM이나 헤더는 게임서버 다른 라이브러리에 정의된것이라 가정하고 코드베이스에 추가 금지.

## 🔧 내부 동작

1. **StartQuery 호출** (싱글 게임 스레드)
2. **CID별 작업 큐 확인**
   - 큐 비어있음: 즉시 사용 가능한 워커가 처리
   - 큐에 작업 있음: 큐에 추가, 이전 작업 콜백에서 처리
3. **워커 스레드가 논블로킹으로 쿼리 전송**
4. **쿼리 완료 시 콜백 (어떤 워커든 처리 가능)**
5. **결과를 싱글 게임 스레드로 전달**
6. **게임 스레드에서 RecordSet으로 결과 처리**

## 💡 성능 최적화

- PreparedStatement 캐싱 (LRU)
- 논블로킹 쿼리 전송으로 워커 활용도 극대화
- 32개 커넥션을 16개 워커가 효율적으로 사용
- CID별 순서 보장으로 트랜잭션 최소화
- 데드락 자동 재시도 (RetryPolicy)

## 🎯 설계 목표 예시

```
예시: 유저 32번이 프로시저 호출
1. 워커1이 CID 32 큐 확인 → 비어있음 → 즉시 호출
2. 워커1이 논블로킹으로 쿼리 전송 후 다른 CID 처리
3. 쿼리 완료 시 워커3이 콜백 처리
4. 워커3이 CID 32 큐에서 다음 작업 확인
5. 모든 결과는 싱글 게임 스레드로 전달
```

이를 통해 16개 워커가 32개 커넥션을 모두 활용하여 고효율 처리 가능

---

게임서버에서 StartQuery 호출 예제 

NSDataBaseManager::GetInstance()->StartQuery<SpUpdateGuildAcceptLimit>(gateConnection, dataSerializer)
	.Then([this, clientInfo](std::shared_ptr<NSQueryData> spQueryData)
		{
            //게임서버로직스레드에서 처리하는 콜백
			QueryResult_UpdateGuildAcceptLimit(clientInfo, spQueryData.get());
		});

**이 규칙을 준수하면 게임서버 코드 변경 없이 안정적인 DB 처리가 가능합니다.**