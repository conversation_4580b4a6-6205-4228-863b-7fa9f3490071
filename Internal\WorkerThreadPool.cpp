#include "stdafx.h"
#include "WorkerThreadPool.h"
#include "../Diagnostics/NSLogger.h"

namespace Database
{

WorkerThreadPool::~WorkerThreadPool()
{
    Stop();
}

bool WorkerThreadPool::Start(uint32_t threadCount)
{
    if (m_running.load())
    {
        LOGW << "WorkerThreadPool already running";
        return false;
    }
    
    m_running = true;
    m_stopRequested = false;
    
    // 워커 스레드 생성
    m_workerThreads.reserve(threadCount);
    for (uint32_t i = 0; i < threadCount; ++i)
    {
        m_workerThreads.emplace_back(&WorkerThreadPool::WorkerThreadFunc, this);
    }
    
    LOGI << "WorkerThreadPool started with " << threadCount << " threads";
    return true;
}

void WorkerThreadPool::Stop()
{
    if (!m_running.load())
        return;
    
    // 중지 요청
    m_stopRequested = true;
    m_workCV.notify_all();
    
    // 모든 워커 스레드 종료 대기
    for (auto& thread : m_workerThreads)
    {
        if (thread.joinable())
        {
            thread.join();
        }
    }
    
    m_workerThreads.clear();
    m_running = false;
    
    // 남은 작업 정리
    std::lock_guard<std::mutex> lock(m_workQueueMutex);
    while (!m_workQueue.empty())
    {
        m_workQueue.pop();
    }
    
    LOGI << "WorkerThreadPool stopped";
}

void WorkerThreadPool::PostWork(std::function<void()> work)
{
    if (!work || !m_running.load())
        return;
    
    {
        std::lock_guard<std::mutex> lock(m_workQueueMutex);
        m_workQueue.push(std::move(work));
    }
    
    m_workCV.notify_one();
}

size_t WorkerThreadPool::GetPendingWorkCount() const
{
    std::lock_guard<std::mutex> lock(m_workQueueMutex);
    return m_workQueue.size();
}

void WorkerThreadPool::WorkerThreadFunc()
{
    while (!m_stopRequested.load())
    {
        std::function<void()> work;
        
        {
            std::unique_lock<std::mutex> lock(m_workQueueMutex);
            
            // 작업이 있거나 중지 요청이 올 때까지 대기
            m_workCV.wait(lock, [this] {
                return !m_workQueue.empty() || m_stopRequested.load();
            });
            
            if (m_stopRequested.load())
                break;
            
            if (!m_workQueue.empty())
            {
                work = std::move(m_workQueue.front());
                m_workQueue.pop();
            }
        }
        
        // 작업 실행
        if (work)
        {
            try
            {
                work();
            }
            catch (const std::exception& e)
            {
                LOGE << "Worker thread exception: " << e.what();
            }
            catch (...)
            {
                LOGE << "Worker thread unknown exception";
            }
        }
    }
}

} // namespace Database